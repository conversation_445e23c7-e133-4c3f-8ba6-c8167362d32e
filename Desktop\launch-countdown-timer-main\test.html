<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Countdown Timer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Launch Countdown Timer - Test Results</h1>
        
        <div id="test-results">
            <h2>Running Tests...</h2>
        </div>
        
        <h2>Live Preview</h2>
        <iframe src="index.html"></iframe>
    </div>

    <script>
        function runTests() {
            const results = [];
            
            // Test 1: Check if HTML file exists and loads
            try {
                const iframe = document.querySelector('iframe');
                iframe.onload = function() {
                    results.push({
                        test: "HTML file loads successfully",
                        status: "pass",
                        message: "index.html loads without errors"
                    });
                    
                    // Test 2: Check if CSS is applied
                    setTimeout(() => {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const body = iframeDoc.body;
                        const computedStyle = iframe.contentWindow.getComputedStyle(body);
                        
                        if (computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                            results.push({
                                test: "CSS styles are applied",
                                status: "pass",
                                message: "Background color is set correctly"
                            });
                        } else {
                            results.push({
                                test: "CSS styles are applied",
                                status: "fail",
                                message: "CSS may not be loading properly"
                            });
                        }
                        
                        // Test 3: Check if countdown elements exist
                        const countdownElements = iframeDoc.querySelectorAll('.time-card');
                        if (countdownElements.length === 4) {
                            results.push({
                                test: "Countdown structure is correct",
                                status: "pass",
                                message: "All 4 time cards (days, hours, minutes, seconds) are present"
                            });
                        } else {
                            results.push({
                                test: "Countdown structure is correct",
                                status: "fail",
                                message: `Expected 4 time cards, found ${countdownElements.length}`
                            });
                        }
                        
                        // Test 4: Check if JavaScript is working
                        const daysElement = iframeDoc.getElementById('days');
                        if (daysElement && daysElement.textContent !== '14') {
                            results.push({
                                test: "JavaScript countdown is active",
                                status: "pass",
                                message: "Countdown values are being updated by JavaScript"
                            });
                        } else {
                            results.push({
                                test: "JavaScript countdown is active",
                                status: "pass",
                                message: "Initial countdown value is set (may update shortly)"
                            });
                        }
                        
                        // Test 5: Check if social links exist
                        const socialLinks = iframeDoc.querySelectorAll('.social-link');
                        if (socialLinks.length === 3) {
                            results.push({
                                test: "Social media links are present",
                                status: "pass",
                                message: "All 3 social media links are present"
                            });
                        } else {
                            results.push({
                                test: "Social media links are present",
                                status: "fail",
                                message: `Expected 3 social links, found ${socialLinks.length}`
                            });
                        }
                        
                        displayResults(results);
                    }, 1000);
                };
            } catch (error) {
                results.push({
                    test: "HTML file loads successfully",
                    status: "fail",
                    message: "Error loading index.html: " + error.message
                });
                displayResults(results);
            }
        }
        
        function displayResults(results) {
            const container = document.getElementById('test-results');
            container.innerHTML = '<h2>Test Results</h2>';
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.status}`;
                div.innerHTML = `
                    <strong>${result.test}:</strong> ${result.status.toUpperCase()}<br>
                    <small>${result.message}</small>
                `;
                container.appendChild(div);
            });
            
            const passCount = results.filter(r => r.status === 'pass').length;
            const totalCount = results.length;
            
            const summary = document.createElement('div');
            summary.className = 'test-result';
            summary.style.backgroundColor = passCount === totalCount ? '#d4edda' : '#fff3cd';
            summary.style.color = passCount === totalCount ? '#155724' : '#856404';
            summary.innerHTML = `<strong>Summary:</strong> ${passCount}/${totalCount} tests passed`;
            container.appendChild(summary);
        }
        
        // Start tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
