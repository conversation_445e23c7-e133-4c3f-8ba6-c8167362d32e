// Launch Countdown Timer JavaScript

class CountdownTimer {
  constructor() {
    // Set target date to 14 days from now
    this.targetDate = new Date();
    this.targetDate.setDate(this.targetDate.getDate() + 14);
    
    // Get DOM elements
    this.elements = {
      days: document.getElementById('days'),
      hours: document.getElementById('hours'),
      minutes: document.getElementById('minutes'),
      seconds: document.getElementById('seconds'),
      daysBack: document.getElementById('days-back'),
      hoursBack: document.getElementById('hours-back'),
      minutesBack: document.getElementById('minutes-back'),
      secondsBack: document.getElementById('seconds-back')
    };
    
    // Store previous values for flip animation
    this.previousValues = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    };
    
    // Initialize timer
    this.updateCountdown();
    this.startTimer();
  }
  
  calculateTimeRemaining() {
    const now = new Date().getTime();
    const distance = this.targetDate.getTime() - now;
    
    if (distance < 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        expired: true
      };
    }
    
    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    return {
      days,
      hours,
      minutes,
      seconds,
      expired: false
    };
  }
  
  formatNumber(num) {
    return num.toString().padStart(2, '0');
  }
  
  flipCard(cardType, newValue) {
    const card = document.querySelector(`.time-card:nth-child(${this.getCardIndex(cardType)}) .flip-card`);
    const frontElement = this.elements[cardType];
    const backElement = this.elements[cardType + 'Back'];
    
    // Set the back card to the new value
    backElement.textContent = this.formatNumber(newValue);
    
    // Add flipping class to trigger animation
    card.classList.add('flipping');
    
    // After animation completes, update front card and remove flipping class
    setTimeout(() => {
      frontElement.textContent = this.formatNumber(newValue);
      card.classList.remove('flipping');
    }, 300);
  }
  
  getCardIndex(cardType) {
    const indices = {
      days: 1,
      hours: 2,
      minutes: 3,
      seconds: 4
    };
    return indices[cardType];
  }
  
  updateCountdown() {
    const timeRemaining = this.calculateTimeRemaining();
    
    if (timeRemaining.expired) {
      this.handleExpiration();
      return;
    }
    
    // Check for changes and trigger flip animations
    Object.keys(timeRemaining).forEach(unit => {
      if (unit !== 'expired' && timeRemaining[unit] !== this.previousValues[unit]) {
        this.flipCard(unit, timeRemaining[unit]);
        this.previousValues[unit] = timeRemaining[unit];
      }
    });
    
    // Update display without animation for initial load
    if (this.isInitialLoad()) {
      this.elements.days.textContent = this.formatNumber(timeRemaining.days);
      this.elements.hours.textContent = this.formatNumber(timeRemaining.hours);
      this.elements.minutes.textContent = this.formatNumber(timeRemaining.minutes);
      this.elements.seconds.textContent = this.formatNumber(timeRemaining.seconds);
      
      this.elements.daysBack.textContent = this.formatNumber(timeRemaining.days);
      this.elements.hoursBack.textContent = this.formatNumber(timeRemaining.hours);
      this.elements.minutesBack.textContent = this.formatNumber(timeRemaining.minutes);
      this.elements.secondsBack.textContent = this.formatNumber(timeRemaining.seconds);
      
      this.previousValues = { ...timeRemaining };
    }
  }
  
  isInitialLoad() {
    return Object.values(this.previousValues).every(val => val === 0);
  }
  
  handleExpiration() {
    // When countdown expires, show all zeros
    Object.keys(this.elements).forEach(key => {
      if (!key.includes('Back')) {
        this.elements[key].textContent = '00';
      }
    });
    
    // Stop the timer
    if (this.timer) {
      clearInterval(this.timer);
    }
    
    // Optional: Add expired state styling or message
    document.body.classList.add('countdown-expired');
  }
  
  startTimer() {
    // Update immediately
    this.updateCountdown();
    
    // Then update every second
    this.timer = setInterval(() => {
      this.updateCountdown();
    }, 1000);
  }
  
  stop() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
}

// Initialize countdown when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const countdown = new CountdownTimer();
  
  // Store reference globally for debugging
  window.countdown = countdown;
});

// Add some additional interactive features
document.addEventListener('DOMContentLoaded', () => {
  // Add click handlers for social links (optional enhancement)
  const socialLinks = document.querySelectorAll('.social-link');
  socialLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      // You can add actual social media URLs here
      console.log('Social link clicked:', link.getAttribute('aria-label'));
    });
  });
  
  // Add keyboard accessibility
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      // Optional: Add any escape key functionality
    }
  });
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CountdownTimer;
}
