/* CSS Reset and Variables */
:root {
  /* Colors */
  --grayish-blue: hsl(237, 18%, 59%);
  --soft-red: hsl(345, 95%, 68%);
  --white: hsl(0, 0%, 100%);
  --dark-desaturated-blue: hsl(236, 21%, 26%);
  --very-dark-blue: hsl(235, 16%, 14%);
  --very-dark-mostly-black-blue: hsl(234, 17%, 12%);
  
  /* Typography */
  --font-family: 'Red Hat Text', sans-serif;
  --font-weight: 700;
  --font-size-body: 14px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight);
  font-size: var(--font-size-body);
  background-color: var(--very-dark-mostly-black-blue);
  color: var(--white);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Container and Layout */
.container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Elements */
.bg-stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('./images/bg-stars.svg');
  background-repeat: repeat;
  background-size: auto;
  z-index: 1;
}

.bg-hills {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background-image: url('./images/pattern-hills.svg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center bottom;
  z-index: 2;
}

/* Main Content */
.content {
  position: relative;
  z-index: 3;
  text-align: center;
  padding: 2rem;
}

.title {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: var(--font-weight);
  color: var(--white);
  margin-bottom: 4rem;
  letter-spacing: 0.3em;
  text-transform: uppercase;
}

/* Countdown Timer */
.countdown {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 6rem;
  flex-wrap: wrap;
}

.time-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* Flip Card Styling */
.flip-card {
  position: relative;
  width: 80px;
  height: 80px;
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card.flipping .flip-card-inner {
  transform: rotateX(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 8px;
  background: linear-gradient(180deg, var(--dark-desaturated-blue) 0%, var(--very-dark-blue) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 0 var(--very-dark-mostly-black-blue);
  position: relative;
}

.flip-card-front::before,
.flip-card-back::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--very-dark-mostly-black-blue);
  z-index: 2;
}

.flip-card-back {
  transform: rotateX(180deg);
}

.number {
  font-size: 2.5rem;
  font-weight: var(--font-weight);
  color: var(--soft-red);
  line-height: 1;
}

.label {
  font-size: 0.75rem;
  color: var(--grayish-blue);
  text-transform: uppercase;
  letter-spacing: 0.3em;
  font-weight: var(--font-weight);
}

/* Social Links */
.social-links {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;
}

.social-link {
  transition: transform 0.3s ease;
}

.social-link:hover {
  transform: scale(1.2);
}

.social-link img {
  width: 24px;
  height: 24px;
  filter: brightness(0) saturate(100%) invert(100%);
  transition: filter 0.3s ease;
}

.social-link:hover img {
  filter: brightness(0) saturate(100%) invert(69%) sepia(73%) saturate(1969%) hue-rotate(321deg) brightness(99%) contrast(94%);
}

/* Attribution */
.attribution {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px;
  text-align: center;
  color: var(--grayish-blue);
  z-index: 4;
}

.attribution a {
  color: var(--soft-red);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .countdown {
    gap: 1rem;
    margin-bottom: 4rem;
  }
  
  .flip-card {
    width: 70px;
    height: 70px;
  }
  
  .number {
    font-size: 2rem;
  }
  
  .title {
    margin-bottom: 3rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .bg-hills {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .countdown {
    gap: 0.75rem;
  }
  
  .flip-card {
    width: 60px;
    height: 60px;
  }
  
  .number {
    font-size: 1.5rem;
  }
  
  .label {
    font-size: 0.625rem;
  }
  
  .social-links {
    gap: 1.5rem;
  }
  
  .social-link img {
    width: 20px;
    height: 20px;
  }
}

@media (min-width: 1440px) {
  .flip-card {
    width: 100px;
    height: 100px;
  }
  
  .number {
    font-size: 3rem;
  }
  
  .countdown {
    gap: 2.5rem;
  }
}
