# Frontend Mentor - Launch countdown timer solution

This is a solution to the [Launch countdown timer challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/launch-countdown-timer-N0XkGfyz-). Frontend Mentor challenges help you improve your coding skills by building realistic projects. 

## Table of contents

- [Overview](#overview)
  - [The challenge](#the-challenge)
  - [Screenshot](#screenshot)
  - [Links](#links)
- [My process](#my-process)
  - [Built with](#built-with)
  - [What I learned](#what-i-learned)
  - [Continued development](#continued-development)
  - [Useful resources](#useful-resources)
- [Author](#author)
- [Acknowledgments](#acknowledgments)


## Overview

### The challenge

Users should be able to:

- See hover states for all interactive elements on the page
- See a live countdown timer that ticks down every second (start the count at 14 days)
- **Bonus**: When a number changes, make the card flip from the middle


![Design preview for the Launch countdown timer coding challenge](./design/desktop-preview.jpg)


## ✨ Features Implemented

✅ **Live countdown timer** - Counts down from 14 days from the current date
✅ **Flip card animations** - Cards flip when numbers change
✅ **Responsive design** - Works on mobile (375px) and desktop (1440px)
✅ **Hover states** - Interactive social media links with hover effects
✅ **Semantic HTML** - Proper accessibility and structure
✅ **Modern CSS** - CSS Grid, Flexbox, and CSS custom properties
✅ **Vanilla JavaScript** - No external dependencies

## 🚀 How to Run

1. Open `index.html` in your web browser
2. Or use the test page: `test.html` for validation
3. The countdown will automatically start from 14 days

## 🛠️ Technical Implementation

### HTML Structure
- Semantic HTML5 with proper accessibility
- Flip card structure for animations
- Social media links with proper ARIA labels

### CSS Features
- CSS custom properties for consistent theming
- Responsive design with mobile-first approach
- 3D flip animations using CSS transforms
- Background images (stars and hills patterns)
- Hover effects and transitions

### JavaScript Functionality
- Object-oriented countdown timer class
- Real-time updates every second
- Flip animation triggers when values change
- Proper number formatting with leading zeros
- Graceful handling of countdown expiration

## 📁 File Structure

```
launch-countdown-timer-main/
├── index.html          # Main HTML file
├── style.css           # All CSS styles and animations
├── script.js           # JavaScript countdown functionality
├── test.html           # Test page for validation
├── images/             # SVG icons and background images
│   ├── bg-stars.svg
│   ├── pattern-hills.svg
│   ├── icon-facebook.svg
│   ├── icon-instagram.svg
│   ├── icon-pinterest.svg
│   └── favicon-32x32.png
├── design/             # Design reference images
└── README.md           # This file
```

## 🎨 Design Specifications

- **Colors**: Dark theme with grayish blue and soft red accents
- **Typography**: Red Hat Text font family, 700 weight
- **Layout**: Responsive design for mobile (375px) and desktop (1440px)
- **Animations**: 3D flip cards with smooth transitions

## 🧪 Testing

Run `test.html` in your browser to see automated tests that verify:
- HTML structure is correct
- CSS styles are applied
- JavaScript countdown is working
- All interactive elements are present


## Deploying your project

- [GitHub Pages](https://pages.github.com/)
- [Vercel](https://vercel.com/)
- [Netlify](https://www.netlify.com/)

## Author

- Frontend Mentor - [@Ayokanmi-Adejola](https://www.frontendmentor.io/profile/Ayokanmi-Adejola)
