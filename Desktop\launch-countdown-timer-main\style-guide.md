# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Grayish blue: hsl(237, 18%, 59%)
- Soft red: hsl(345, 95%, 68%)

### Neutral

- White: hsl(0, 0%, 100%)
- Dark desaturated blue: hsl(236, 21%, 26%)
- Very dark blue: hsl(235, 16%, 14%)
- Very dark (mostly black) blue: hsl(234, 17%, 12%)

## Typography

### Body Copy

- Font size: 14px

### Font

- Family: [Red Hat Text](https://fonts.google.com/specimen/Red+Hat+Text)
- Weights: 700

## Icons

We provide the required social icons. But, if you prefer, you can use a font icon library. Some suggestions can be found below:

- [Font Awesome](https://fontawesome.com)
- [IcoMoon](https://icomoon.io)
- [Ionicons](https://ionicons.com)

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
